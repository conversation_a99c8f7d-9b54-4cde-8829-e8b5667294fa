# Task Master AI Setup Guide

Task Master AI has been successfully installed in your project! This guide will help you get started.

## What is Task Master AI?

Task Master AI is a comprehensive task management system designed for AI-driven development that works with editors like Cursor. It helps you:

- Break down complex projects into manageable tasks
- Track progress and dependencies
- Generate detailed implementation plans
- Integrate with AI assistants for better project management

## Installation Status

✅ **Package Installed**: task-master-ai v0.19.0  
✅ **Configuration Created**: `taskmaster.config.json`  
✅ **Environment Template**: `.env.example`  
✅ **NPM Scripts Added**: `npm run taskmaster` and `npm run tm`  

## Quick Start

### 1. Set Up Environment Variables

Copy the example environment file and add your API keys:

```bash
cp .env.example .env
```

Edit `.env` and add your API keys (at minimum, you'll need an Anthropic API key):

```bash
ANTHROPIC_API_KEY="your_anthropic_api_key_here"
```

### 2. Initialize Task Master

Since the automatic initialization had issues, you can manually create the basic structure:

```bash
# Create the taskmaster directory structure
mkdir -p .taskmaster/tasks
mkdir -p .taskmaster/reports
mkdir -p .taskmaster/docs/research
mkdir -p .taskmaster/templates

# Copy the configuration file to the correct location
cp taskmaster.config.json .taskmaster/config.json
```

### 3. Create a Project Requirements Document (PRD)

Create a file describing your project requirements. You can start with a simple text file:

```bash
# Create a basic PRD for your holy-text-gather project
cat > project_requirements.txt << 'EOF'
# Holy Text Gather - Project Requirements

## Overview
A React application for gathering and displaying religious texts from multiple traditions including Christianity, Buddhism, Hinduism, and Islam.

## Key Features
- Display specific Bible verses when requested (e.g., Matthew 7)
- Provide thoughtful commentary and analysis on religious passages
- Support multiple religious traditions
- AI-powered guidance on emotional topics
- Pause/resume/interrupt functionality for follow-up questions
- Red and black color scheme with glowing visual elements
- Soft, slow pulse animations for interactive feedback

## Technical Requirements
- React with TypeScript
- Vite build system
- Modern UI components
- Responsive design
- API integration for religious text sources

## User Experience
- Conversational, natural language interface
- Display actual verse text with AI commentary
- Interactive elements with visual feedback
- Support for follow-up questions during interactions
EOF
```

### 4. Parse Your PRD to Generate Tasks

```bash
npx task-master parse-prd project_requirements.txt
```

### 5. View Your Tasks

```bash
npx task-master list
```

## Available Commands

You can use task-master-ai through several methods:

### NPM Scripts (Recommended)
```bash
npm run taskmaster list          # List all tasks
npm run tm next                  # Get next task to work on
npm run tm show 1                # Show details for task 1
```

### Direct CLI
```bash
npx task-master list             # List all tasks
npx task-master next             # Get next task to work on
npx task-master show 1           # Show details for task 1
```

## Common Workflows

### Starting a Work Session
```bash
npm run tm list                  # See all tasks
npm run tm next                  # Get next task to work on
```

### Adding New Tasks
```bash
npm run tm add-task -p "Implement user authentication"
```

### Updating Task Status
```bash
npm run tm set-status -i 1 -s "in-progress"    # Mark task 1 as in progress
npm run tm set-status -i 1 -s "done"           # Mark task 1 as done
```

### Breaking Down Complex Tasks
```bash
npm run tm expand -i 1           # Break task 1 into subtasks
```

## Configuration

The main configuration is in `.taskmaster/config.json`. Key settings:

- **Project Name**: "Holy Text Gather"
- **AI Models**: Configured for Anthropic Claude
- **Default Priority**: Medium
- **Default Subtasks**: 5

## Troubleshooting

### If commands don't work:
1. Make sure you have API keys set in `.env`
2. Check that the `.taskmaster` directory exists
3. Try running `npx task-master init` if needed

### For more help:
- Check the task-master-ai documentation
- Use `npm run tm --help` for command help
- Look at the example files in `node_modules/task-master-ai/assets/`

## Next Steps

1. Set up your API keys in `.env`
2. Create and parse your PRD
3. Start working through your generated tasks
4. Use the AI features to expand and update tasks as needed

Happy coding! 🚀
